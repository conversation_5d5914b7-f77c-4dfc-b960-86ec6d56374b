info:  Game: AA2Play.exe
info:  DXVK: v2.2
info:  Vulkan: Found vkGetInstanceProcAddr in vulkan-1.dll @ 0x700b1760
info:  Built-in extension providers:
info:    Win32 WSI
info:    OpenVR
info:    OpenXR
info:  OpenVR: could not open registry key, status 2
info:  OpenVR: Failed to locate module
info:  Enabled instance extensions:
info:    VK_KHR_get_surface_capabilities2
info:    VK_KHR_surface
info:    VK_KHR_win32_surface
warn:  Skipping Vulkan 1.2 adapter: Microsoft Direct3D12 (Intel(R) Iris(R) Xe Graphics)
warn:  Skipping Vulkan 1.2 adapter: Microsoft Direct3D12 (Microsoft Basic Render Driver)
info:  D3D9: VK_FORMAT_D16_UNORM_S8_UINT -> VK_FORMAT_D24_UNORM_S8_UINT
info:  Intel(R) Iris(R) Xe Graphics:
info:    Driver : Intel Corporation 103.769.0
info:    Memory Heap[0]: 
info:      Size: 8008 MiB
info:      Flags: 0x1
info:      Memory Type[0]: Property Flags = 0x1
info:      Memory Type[1]: Property Flags = 0x7
info:      Memory Type[2]: Property Flags = 0xf
info:  Process set as DPI aware
info:  D3D9: VK_FORMAT_D16_UNORM_S8_UINT -> VK_FORMAT_D24_UNORM_S8_UINT
info:  Intel(R) Iris(R) Xe Graphics:
info:    Driver : Intel Corporation 103.769.0
info:    Memory Heap[0]: 
info:      Size: 8008 MiB
info:      Flags: 0x1
info:      Memory Type[0]: Property Flags = 0x1
info:      Memory Type[1]: Property Flags = 0x7
info:      Memory Type[2]: Property Flags = 0xf
info:  Process set as DPI aware
