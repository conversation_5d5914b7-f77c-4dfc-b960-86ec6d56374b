@echo off
chcp 65001 >nul
echo 正在修复AAUnlimited路径问题...
echo.

REM 设置当前目录为脚本所在目录
cd /d "%~dp0"

REM 检查是否存在AAUnlimited文件夹
if not exist "AAUnlimited" (
    echo 错误：找不到AAUnlimited文件夹
    pause
    exit /b 1
)

REM 备份原始配置文件
if exist "AAUnlimited\config.lua" (
    copy "AAUnlimited\config.lua" "AAUnlimited\config.lua.backup" >nul
    echo 已备份配置文件
)

REM 创建临时修复配置
echo -- 临时修复配置 > "AAUnlimited\tempfix.lua"
echo bUsePPeX = false >> "AAUnlimited\tempfix.lua"
echo bUsePP2 = false >> "AAUnlimited\tempfix.lua"
echo logPrio = 1 >> "AAUnlimited\tempfix.lua"

REM 尝试重新注册AAUnlimited
echo 正在重新注册AAUnlimited...
cd AAUnlimited
if exist "【安装】.bat" (
    call "【安装】.bat"
) else (
    echo 警告：找不到安装脚本
)

cd ..

echo.
echo 修复完成！请尝试重新启动游戏。
echo 如果问题仍然存在，建议将游戏移动到英文路径。
echo.
pause
