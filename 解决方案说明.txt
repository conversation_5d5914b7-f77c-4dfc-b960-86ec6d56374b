AA2游戏"Resource Analysis failed"错误解决方案
==============================================

问题描述：
游戏启动时出现"Resource Analysis failed"弹框，点击"是"或"否"会导致程序卡死，
点击"取消"可以进入游戏但界面白屏。

问题原因：
1. 游戏路径包含中文字符，导致AAUnlimited无法正确识别可执行文件类型
2. AAUnlimited的DLL注入可能失败
3. 配置文件可能损坏

解决方案：

方案1：移动到英文路径（强烈推荐）
=====================================
1. 将整个游戏文件夹移动到纯英文路径，例如：
   - C:\Games\AA2\
   - D:\AA2\
   - E:\AA2Game\

2. 移动后重新运行AAUnlimited安装：
   - 进入AAUnlimited文件夹
   - 运行"【安装】.bat"
   - 选择对应的AA2Play.exe和AA2Edit.exe

方案2：当前路径修复（已执行）
============================
已经为您执行了以下修复：
1. ✓ 备份了原始配置文件
2. ✓ 清除了可能损坏的保存配置
3. ✓ 重新安装了AAUnlimited
4. ✓ 创建了安全启动脚本

方案3：手动修复步骤
==================
如果上述方案仍无效，请手动执行：

1. 删除以下文件（如果存在）：
   - AAUnlimited\savedconfig.lua
   - AAUnlimited\logfile.txt

2. 编辑AAUnlimited\config.lua，确保：
   - bUsePPeX = true
   - logPrio = 1

3. 重新注册AAUnlimited：
   - 以管理员身份运行命令提示符
   - 进入游戏目录
   - 执行：rundll32 AAUnlimited\AAUnlimitedDLL.dll,_AA2UPatcher@16

4. 使用安全模式启动：
   - 运行start_aa2_safe.bat而不是直接运行AA2Play.exe

测试步骤：
=========
1. 首先尝试使用start_aa2_safe.bat启动游戏
2. 如果仍出现错误，检查AAUnlimited\logfile.txt查看详细错误信息
3. 如果问题持续，强烈建议移动到英文路径

注意事项：
=========
- 确保以管理员权限运行游戏
- 关闭杀毒软件的实时保护（可能误报AAUnlimited）
- 确保Windows Defender没有阻止AAUnlimited的DLL文件

如果所有方案都无效：
==================
1. 完全重新下载游戏和AAUnlimited
2. 安装到纯英文路径
3. 确保系统支持.NET Framework 4.0或更高版本

联系支持：
=========
如果问题仍然存在，请提供：
- AAUnlimited\logfile.txt的内容
- 游戏安装路径
- Windows版本信息
- 是否使用了杀毒软件
